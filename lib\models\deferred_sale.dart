class DeferredSale {
  final int? id;
  final String customerName;
  final String customerPhone;
  final String productName;
  final double totalAmount;
  final DateTime saleDate;
  final DateTime dueDate;
  final int durationMonths;
  final String status; // 'active', 'completed', 'overdue'
  final String? notes;

  DeferredSale({
    this.id,
    required this.customerName,
    required this.customerPhone,
    required this.productName,
    required this.totalAmount,
    required this.saleDate,
    required this.dueDate,
    required this.durationMonths,
    this.status = 'active',
    this.notes,
  });

  // حساب الأيام المتبقية
  int get remainingDays {
    final now = DateTime.now();
    final difference = dueDate.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  // حساب الأيام المنقضية
  int get elapsedDays {
    final now = DateTime.now();
    return now.difference(saleDate).inDays;
  }

  // حساب النسبة المئوية للوقت المنقضي
  double get progressPercentage {
    final totalDays = dueDate.difference(saleDate).inDays;
    final elapsed = elapsedDays;
    if (totalDays <= 0) return 100.0;
    final percentage = (elapsed / totalDays) * 100;
    return percentage > 100 ? 100.0 : percentage;
  }

  // تحديد حالة المبيعة
  String get currentStatus {
    final now = DateTime.now();
    if (status == 'completed') return 'completed';
    if (now.isAfter(dueDate)) return 'overdue';
    return 'active';
  }

  // تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'productName': productName,
      'totalAmount': totalAmount,
      'saleDate': saleDate.millisecondsSinceEpoch,
      'dueDate': dueDate.millisecondsSinceEpoch,
      'durationMonths': durationMonths,
      'status': status,
      'notes': notes,
    };
  }

  // إنشاء من Map
  factory DeferredSale.fromMap(Map<String, dynamic> map) {
    return DeferredSale(
      id: map['id'],
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      productName: map['productName'] ?? '',
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      saleDate: DateTime.fromMillisecondsSinceEpoch(map['saleDate']),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      durationMonths: map['durationMonths'] ?? 0,
      status: map['status'] ?? 'active',
      notes: map['notes'],
    );
  }

  // نسخ مع تعديل
  DeferredSale copyWith({
    int? id,
    String? customerName,
    String? customerPhone,
    String? productName,
    double? totalAmount,
    DateTime? saleDate,
    DateTime? dueDate,
    int? durationMonths,
    String? status,
    String? notes,
  }) {
    return DeferredSale(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      productName: productName ?? this.productName,
      totalAmount: totalAmount ?? this.totalAmount,
      saleDate: saleDate ?? this.saleDate,
      dueDate: dueDate ?? this.dueDate,
      durationMonths: durationMonths ?? this.durationMonths,
      status: status ?? this.status,
      notes: notes ?? this.notes,
    );
  }
}
