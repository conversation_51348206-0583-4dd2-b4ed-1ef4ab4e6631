import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/deferred_sale.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'deferred_sales.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE deferred_sales(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customerName TEXT NOT NULL,
        customerPhone TEXT NOT NULL,
        productName TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        saleDate INTEGER NOT NULL,
        dueDate INTEGER NOT NULL,
        durationMonths INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        notes TEXT
      )
    ''');
  }

  // إضافة مبيعة جديدة
  Future<int> insertSale(DeferredSale sale) async {
    final db = await database;
    return await db.insert('deferred_sales', sale.toMap());
  }

  // الحصول على جميع المبيعات
  Future<List<DeferredSale>> getAllSales() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'deferred_sales',
      orderBy: 'saleDate DESC',
    );
    return List.generate(maps.length, (i) => DeferredSale.fromMap(maps[i]));
  }

  // الحصول على المبيعات حسب الحالة
  Future<List<DeferredSale>> getSalesByStatus(String status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'deferred_sales',
      where: 'status = ?',
      whereArgs: [status],
      orderBy: 'dueDate ASC',
    );
    return List.generate(maps.length, (i) => DeferredSale.fromMap(maps[i]));
  }

  // البحث في المبيعات
  Future<List<DeferredSale>> searchSales(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'deferred_sales',
      where: 'customerName LIKE ? OR productName LIKE ? OR customerPhone LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'saleDate DESC',
    );
    return List.generate(maps.length, (i) => DeferredSale.fromMap(maps[i]));
  }

  // تحديث مبيعة
  Future<int> updateSale(DeferredSale sale) async {
    final db = await database;
    return await db.update(
      'deferred_sales',
      sale.toMap(),
      where: 'id = ?',
      whereArgs: [sale.id],
    );
  }

  // حذف مبيعة
  Future<int> deleteSale(int id) async {
    final db = await database;
    return await db.delete(
      'deferred_sales',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على إحصائيات
  Future<Map<String, dynamic>> getStatistics() async {
    final db = await database;
    
    // إجمالي المبيعات
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count, SUM(totalAmount) as total FROM deferred_sales');
    final totalSales = totalResult.first['count'] as int;
    final totalAmount = (totalResult.first['total'] as double?) ?? 0.0;
    
    // المبيعات النشطة
    final activeResult = await db.rawQuery('SELECT COUNT(*) as count FROM deferred_sales WHERE status = "active"');
    final activeSales = activeResult.first['count'] as int;
    
    // المبيعات المكتملة
    final completedResult = await db.rawQuery('SELECT COUNT(*) as count FROM deferred_sales WHERE status = "completed"');
    final completedSales = completedResult.first['count'] as int;
    
    // المبيعات المتأخرة
    final now = DateTime.now().millisecondsSinceEpoch;
    final overdueResult = await db.rawQuery('SELECT COUNT(*) as count FROM deferred_sales WHERE dueDate < ? AND status = "active"', [now]);
    final overdueSales = overdueResult.first['count'] as int;
    
    return {
      'totalSales': totalSales,
      'totalAmount': totalAmount,
      'activeSales': activeSales,
      'completedSales': completedSales,
      'overdueSales': overdueSales,
    };
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
