import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import 'add_sale_screen.dart';

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen>
    with TickerProviderStateMixin {
  final _quantityController = TextEditingController();
  final _priceController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _productNameController = TextEditingController();

  DateTime _saleDate = DateTime.now();
  DateTime? _dueDate;
  int _selectedMonths = 1;
  double _totalAmount = 0.0;
  bool _useCustomDueDate = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<int> _monthOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
    
    _quantityController.addListener(_calculateTotal);
    _priceController.addListener(_calculateTotal);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _productNameController.dispose();
    super.dispose();
  }

  void _calculateTotal() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final price = double.tryParse(_priceController.text) ?? 0;
    setState(() {
      _totalAmount = quantity * price;
    });
  }

  DateTime get _calculatedDueDate {
    return DateTime(
      _saleDate.year,
      _saleDate.month + _selectedMonths,
      _saleDate.day,
    );
  }

  DateTime get _finalDueDate {
    return _useCustomDueDate && _dueDate != null ? _dueDate! : _calculatedDueDate;
  }

  int get _totalDays {
    return _finalDueDate.difference(_saleDate).inDays;
  }

  void _updateMonthsFromDueDate() {
    if (_dueDate != null) {
      final months = (_dueDate!.year - _saleDate.year) * 12 +
                    (_dueDate!.month - _saleDate.month);
      setState(() {
        _selectedMonths = months > 0 ? months : 1;
      });
    }
  }

  Future<void> _selectSaleDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _saleDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null && picked != _saleDate) {
      setState(() {
        _saleDate = picked;
        if (_useCustomDueDate && _dueDate != null) {
          _updateMonthsFromDueDate();
        }
      });
    }
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? _calculatedDueDate,
      firstDate: _saleDate.add(const Duration(days: 1)),
      lastDate: _saleDate.add(const Duration(days: 365 * 5)),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null) {
      setState(() {
        _dueDate = picked;
        _useCustomDueDate = true;
        _updateMonthsFromDueDate();
      });
    }
  }

  void _proceedToSale() {
    if (_totalAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال الكمية والسعر أولاً'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddSaleScreen(
          prefilledData: {
            'customerName': _customerNameController.text,
            'customerPhone': _customerPhoneController.text,
            'productName': _productNameController.text,
            'totalAmount': _totalAmount,
            'saleDate': _saleDate,
            'dueDate': _finalDueDate,
            'durationMonths': _selectedMonths,
            'useCustomDueDate': _useCustomDueDate,
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('حاسبة المبيعات'),
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم الحساب الأساسي
                _buildCalculationSection(),
                const SizedBox(height: 24),

                // قسم اختيار المدة
                _buildDurationSection(),
                const SizedBox(height: 24),

                // قسم النتائج
                _buildResultsSection(),
                const SizedBox(height: 24),

                // قسم معلومات إضافية (اختياري)
                _buildOptionalInfoSection(),
                const SizedBox(height: 32),

                // زر المتابعة
                _buildProceedButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalculationSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calculate,
                    color: AppColors.primary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'حساب المبلغ الإجمالي',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  child: _buildAnimatedTextField(
                    controller: _quantityController,
                    label: 'عدد الكارتات',
                    hint: 'أدخل العدد',
                    icon: Icons.inventory,
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildAnimatedTextField(
                    controller: _priceController,
                    label: 'سعر الكارت الواحد',
                    hint: 'أدخل السعر',
                    icon: Icons.attach_money,
                    keyboardType: TextInputType.number,
                    suffix: 'ر.س',
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // عرض المعادلة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.info.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildCalculationPart(_quantityController.text.isEmpty ? '0' : _quantityController.text, 'كارت'),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Icon(Icons.close, color: AppColors.info),
                  ),
                  _buildCalculationPart(_priceController.text.isEmpty ? '0' : _priceController.text, 'ر.س'),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Icon(Icons.drag_handle, color: AppColors.info),
                  ),
                  _buildCalculationPart(
                    NumberFormat('#,##0', 'ar').format(_totalAmount),
                    'ر.س',
                    isResult: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? suffix,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(icon, color: AppColors.primary),
          suffixText: suffix,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.textLight),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
        ),
      ),
    );
  }

  Widget _buildCalculationPart(String value, String unit, {bool isResult = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isResult ? AppColors.success.withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isResult ? AppColors.success : AppColors.info.withOpacity(0.5),
        ),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: isResult ? 18 : 16,
              fontWeight: isResult ? FontWeight.bold : FontWeight.w600,
              color: isResult ? AppColors.success : AppColors.textPrimary,
            ),
          ),
          Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              color: isResult ? AppColors.success : AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.schedule,
                    color: AppColors.secondary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'اختيار مدة السداد',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // تاريخ البيع
            InkWell(
              onTap: _selectSaleDate,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.textLight),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, color: AppColors.secondary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تاريخ البيع',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('dd/MM/yyyy', 'ar').format(_saleDate),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // اختيار الأشهر
            Text(
              'اختر مدة السداد بالأشهر:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _monthOptions.map((months) {
                final isSelected = _selectedMonths == months;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedMonths = months;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.secondary : Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected ? AppColors.secondary : AppColors.textLight,
                          width: isSelected ? 2 : 1,
                        ),
                        boxShadow: isSelected ? [
                          BoxShadow(
                            color: AppColors.secondary.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ] : null,
                      ),
                      child: Text(
                        months == 1 ? 'شهر واحد' : '$months أشهر',
                        style: TextStyle(
                          color: isSelected ? Colors.white : AppColors.textPrimary,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // خيار تحديد تاريخ الاستحقاق يدوياً
            Row(
              children: [
                Checkbox(
                  value: _useCustomDueDate,
                  onChanged: (value) {
                    setState(() {
                      _useCustomDueDate = value ?? false;
                      if (!_useCustomDueDate) {
                        _dueDate = null;
                      }
                    });
                  },
                  activeColor: AppColors.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'تحديد تاريخ الاستحقاق يدوياً',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),

            if (_useCustomDueDate) ...[
              const SizedBox(height: 16),
              InkWell(
                onTap: _selectDueDate,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.secondary),
                    borderRadius: BorderRadius.circular(12),
                    color: AppColors.secondary.withOpacity(0.1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.event, color: AppColors.secondary),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تاريخ الاستحقاق المخصص',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _dueDate != null
                                  ? DateFormat('dd/MM/yyyy', 'ar').format(_dueDate!)
                                  : 'اضغط لاختيار التاريخ',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: _dueDate != null
                                    ? AppColors.textPrimary
                                    : AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [AppColors.success.withOpacity(0.1), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    color: AppColors.success,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'ملخص العملية',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // المبلغ الإجمالي
            _buildResultItem(
              'المبلغ الإجمالي',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س',
                decimalDigits: 0,
              ).format(_totalAmount),
              Icons.attach_money,
              AppColors.success,
              isHighlighted: true,
            ),
            const SizedBox(height: 16),

            // تاريخ الاستحقاق
            _buildResultItem(
              'تاريخ الاستحقاق',
              DateFormat('dd/MM/yyyy', 'ar').format(_finalDueDate),
              Icons.event,
              AppColors.info,
            ),
            const SizedBox(height: 16),

            // عدد الأيام
            _buildResultItem(
              'إجمالي الأيام',
              '$_totalDays يوم',
              Icons.today,
              AppColors.warning,
            ),
            const SizedBox(height: 16),

            // المدة بالأشهر
            _buildResultItem(
              'مدة السداد',
              _selectedMonths == 1 ? 'شهر واحد' : '$_selectedMonths أشهر',
              Icons.schedule,
              AppColors.secondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color, {
    bool isHighlighted = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHighlighted ? color.withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHighlighted ? color : color.withOpacity(0.3),
          width: isHighlighted ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    color: isHighlighted ? color : AppColors.textPrimary,
                    fontSize: isHighlighted ? 18 : 16,
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionalInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ExpansionTile(
        leading: const Icon(Icons.person_add, color: AppColors.primary),
        title: const Text(
          'معلومات إضافية (اختياري)',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildAnimatedTextField(
                  controller: _customerNameController,
                  label: 'اسم العميل',
                  hint: 'أدخل اسم العميل',
                  icon: Icons.person,
                ),
                const SizedBox(height: 16),
                _buildAnimatedTextField(
                  controller: _customerPhoneController,
                  label: 'رقم الهاتف',
                  hint: 'أدخل رقم الهاتف',
                  icon: Icons.phone,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                _buildAnimatedTextField(
                  controller: _productNameController,
                  label: 'اسم المنتج',
                  hint: 'أدخل اسم المنتج',
                  icon: Icons.shopping_bag,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProceedButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _totalAmount > 0 ? _proceedToSale : null,
        icon: const Icon(Icons.arrow_forward, size: 24),
        label: const Text(
          'متابعة إلى البيع',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: _totalAmount > 0 ? AppColors.primary : AppColors.textLight,
          foregroundColor: Colors.white,
          elevation: _totalAmount > 0 ? 8 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadowColor: AppColors.primary.withOpacity(0.3),
        ),
      ),
    );
  }
}
