import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import 'add_sale_screen.dart';

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen>
    with TickerProviderStateMixin {
  final _quantityController = TextEditingController();
  final _priceController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _productNameController = TextEditingController();

  DateTime _saleDate = DateTime.now();
  DateTime? _dueDate;
  int _selectedMonths = 1;
  DateTime _selectedTargetDate = DateTime.now(); // التاريخ المستهدف
  double _totalAmount = 0.0;
  bool _useCustomDueDate = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // دالة للحصول على اسم الشهر بالأرقام الترتيبية
  String _getMonthName(int month) {
    const monthNames = [
      'الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس',
      'السابع', 'الثامن', 'التاسع', 'العاشر', 'الحادي عشر', 'الثاني عشر'
    ];
    return monthNames[month - 1];
  }

  // دالة لحساب التاريخ بإضافة أشهر بشكل صحيح
  DateTime _addMonthsToDate(DateTime date, int months) {
    final targetYear = date.year + ((date.month + months - 1) ~/ 12);
    final targetMonth = ((date.month + months - 1) % 12) + 1;
    return DateTime(targetYear, targetMonth, date.day);
  }

  // دالة لبناء نص المدة
  String _buildDurationText() {
    final saleMonthName = _getMonthName(_saleDate.month);
    final targetMonthName = _getMonthName(_selectedTargetDate.month);
    final monthsText = _selectedMonths == 1 ? 'شهر واحد' : '$_selectedMonths أشهر';

    // إذا كانت السنة مختلفة، أضف السنة
    if (_saleDate.year != _selectedTargetDate.year) {
      return 'من الشهر $saleMonthName ${_saleDate.year} إلى الشهر $targetMonthName ${_selectedTargetDate.year} = $monthsText';
    } else {
      return 'من الشهر $saleMonthName إلى الشهر $targetMonthName = $monthsText';
    }
  }

  // قائمة الأشهر للسنة الحالية والقادمة
  List<Map<String, dynamic>> get _monthOptions {
    final List<Map<String, dynamic>> months = [];
    final saleDate = _saleDate;

    // إضافة أشهر من تاريخ البيع حتى 12 شهر قادم
    for (int i = 0; i < 12; i++) {
      final monthsToAdd = i + 1;
      final targetDate = _addMonthsToDate(saleDate, monthsToAdd);

      months.add({
        'month': targetDate.month,
        'year': targetDate.year,
        'monthsDiff': monthsToAdd,
        'name': _getMonthName(targetDate.month),
        'date': targetDate,
      });
    }

    return months;
  }

  @override
  void initState() {
    super.initState();

    // تهيئة التاريخ المستهدف (شهر واحد من تاريخ البيع)
    _selectedTargetDate = _addMonthsToDate(_saleDate, 1);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();

    _quantityController.addListener(_calculateTotal);
    _priceController.addListener(_calculateTotal);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _productNameController.dispose();
    super.dispose();
  }

  void _calculateTotal() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    // إزالة الفواصل من السعر قبل التحويل
    final priceText = _priceController.text.replaceAll(',', '');
    final price = double.tryParse(priceText) ?? 0;
    setState(() {
      _totalAmount = quantity * price;
    });
  }

  DateTime get _calculatedDueDate {
    return _selectedTargetDate;
  }

  DateTime get _finalDueDate {
    return _useCustomDueDate && _dueDate != null ? _dueDate! : _calculatedDueDate;
  }

  int get _totalDays {
    return _finalDueDate.difference(_saleDate).inDays;
  }

  void _updateMonthsFromDueDate() {
    if (_dueDate != null) {
      final months = (_dueDate!.year - _saleDate.year) * 12 +
                    (_dueDate!.month - _saleDate.month);
      setState(() {
        _selectedMonths = months > 0 ? months : 1;
        _selectedTargetDate = _dueDate!;
      });
    }
  }

  Future<void> _selectSaleDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _saleDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null && picked != _saleDate) {
      setState(() {
        _saleDate = picked;
        // تحديث التاريخ المستهدف بناءً على الأشهر المحددة
        _selectedTargetDate = _addMonthsToDate(picked, _selectedMonths);
        if (_useCustomDueDate && _dueDate != null) {
          _updateMonthsFromDueDate();
        }
      });
    }
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? _calculatedDueDate,
      firstDate: _saleDate.add(const Duration(days: 1)),
      lastDate: _saleDate.add(const Duration(days: 365 * 5)),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null) {
      setState(() {
        _dueDate = picked;
        _useCustomDueDate = true;
        _updateMonthsFromDueDate();
      });
    }
  }

  void _proceedToSale() {
    if (_totalAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال الكمية والسعر أولاً'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddSaleScreen(
          prefilledData: {
            'customerName': _customerNameController.text,
            'customerPhone': _customerPhoneController.text,
            'productName': _productNameController.text,
            'totalAmount': _totalAmount,
            'saleDate': _saleDate,
            'dueDate': _finalDueDate,
            'durationMonths': _selectedMonths,
            'useCustomDueDate': _useCustomDueDate,
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('حاسبة المبيعات'),
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم الحساب الأساسي
                _buildCalculationSection(),
                const SizedBox(height: 24),

                // قسم اختيار المدة
                _buildDurationSection(),
                const SizedBox(height: 24),

                // قسم النتائج
                _buildResultsSection(),
                const SizedBox(height: 24),

                // قسم معلومات إضافية (اختياري)
                _buildOptionalInfoSection(),
                const SizedBox(height: 32),

                // زر المتابعة
                _buildProceedButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalculationSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calculate,
                    color: AppColors.primary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'حساب المبلغ الإجمالي',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildAnimatedTextField(
                    controller: _quantityController,
                    label: 'العدد',
                    hint: '0',
                    icon: Icons.inventory,
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 3,
                  child: _buildAnimatedTextField(
                    controller: _priceController,
                    label: 'السعر',
                    hint: '0',
                    icon: Icons.attach_money,
                    keyboardType: TextInputType.number,
                    suffix: 'د.ع',
                    useThousandsSeparator: true,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // المعادلة الحسابية المبسطة
            Column(
              children: [
                // العنوان
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary,
                        AppColors.primary.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.functions,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'حاسبة المبلغ الإجمالي',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                        // البطاقة الأولى: عدد الكروت
                        _buildFullWidthCalculationCard(
                          title: 'عدد الكروت',
                          value: _quantityController.text.isEmpty ? '0' : _quantityController.text,
                          unit: 'كارت',
                          icon: Icons.credit_card,
                          color: AppColors.secondary,
                        ),

                        const SizedBox(height: 16),

                        // البطاقة الثانية: سعر الكارت
                        _buildFullWidthCalculationCard(
                          title: 'سعر الكارت الواحد',
                          value: _priceController.text.isEmpty
                              ? '0'
                              : NumberFormat('#,##0', 'en').format(double.tryParse(_priceController.text.replaceAll(',', '')) ?? 0),
                          unit: 'دينار عراقي',
                          icon: Icons.attach_money,
                          color: AppColors.warning,
                        ),

                        const SizedBox(height: 16),

                        // البطاقة الثالثة: النتيجة
                        _buildFullWidthCalculationCard(
                          title: 'المبلغ الإجمالي',
                          value: NumberFormat('#,##0', 'en').format(_totalAmount),
                          unit: 'دينار عراقي',
                          icon: Icons.check_circle,
                          color: AppColors.success,
                          isResult: true,
                        ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? suffix,
    bool useThousandsSeparator = false,
  }) {
    return Container(
      height: 60, // تحديد ارتفاع ثابت
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: AppColors.textLight,
            fontSize: 12,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 16,
            ),
          ),
          suffixText: suffix,
          suffixStyle: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 1),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          isDense: true, // جعل الحقل أكثر كثافة
        ),
        onChanged: (value) {
          if (useThousandsSeparator && value.isNotEmpty) {
            // إزالة الفواصل الموجودة
            String cleanValue = value.replaceAll(',', '');

            // التحقق من أن القيمة رقم صحيح
            if (double.tryParse(cleanValue) != null) {
              // تنسيق الرقم مع فاصلة الآلاف
              String formattedValue = NumberFormat('#,##0', 'en').format(double.parse(cleanValue));

              // تحديث النص إذا كان مختلفاً
              if (formattedValue != value) {
                controller.value = TextEditingValue(
                  text: formattedValue,
                  selection: TextSelection.collapsed(offset: formattedValue.length),
                );
              }
            }
          }
          _calculateTotal();
        },
      ),
    );
  }

  // بطاقة بالعرض الكامل للمعادلة (بدون حواف إضافية)
  Widget _buildFullWidthCalculationCard({
    required String title,
    required String value,
    required String unit,
    required IconData icon,
    required Color color,
    bool isResult = false,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isResult
            ? color.withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // المحتوى
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: isResult ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: isResult ? color : AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      unit,
                      style: TextStyle(
                        fontSize: 12,
                        color: isResult
                            ? color.withValues(alpha: 0.8)
                            : AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // مؤشر النتيجة
          if (isResult)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'النتيجة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }





  Widget _buildDurationSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade50],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.schedule,
                    color: AppColors.secondary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'اختيار مدة السداد',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // تاريخ البيع
            InkWell(
              onTap: _selectSaleDate,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.textLight),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, color: AppColors.secondary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'تاريخ البيع',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('dd/MM/yyyy', 'en').format(_saleDate),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // اختيار الأشهر
            Text(
              'اختر مدة السداد بالأشهر:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),

            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: _monthOptions.length,
              itemBuilder: (context, index) {
                final monthData = _monthOptions[index];
                final isSelected = _selectedMonths == monthData['monthsDiff'];

                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: Material(
                    elevation: isSelected ? 8 : 2,
                    borderRadius: BorderRadius.circular(16),
                    shadowColor: isSelected
                        ? AppColors.secondary.withValues(alpha: 0.4)
                        : Colors.black.withValues(alpha: 0.1),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedMonths = monthData['monthsDiff'];
                          _selectedTargetDate = monthData['date'];
                        });
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: isSelected
                              ? LinearGradient(
                                  colors: [
                                    AppColors.secondary,
                                    AppColors.secondary.withValues(alpha: 0.8),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                              : LinearGradient(
                                  colors: [
                                    Colors.white,
                                    Colors.grey.shade50,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                          border: Border.all(
                            color: isSelected
                                ? AppColors.secondary
                                : Colors.grey.shade300,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AnimatedDefaultTextStyle(
                              duration: const Duration(milliseconds: 300),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : AppColors.primary,
                              ),
                              child: Text(monthData['name']),
                            ),
                            const SizedBox(height: 4),
                            AnimatedDefaultTextStyle(
                              duration: const Duration(milliseconds: 300),
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: isSelected
                                    ? Colors.white.withValues(alpha: 0.9)
                                    : AppColors.textSecondary,
                              ),
                              child: Text(monthData['monthsDiff'] == 1 ? 'شهر واحد' : '${monthData['monthsDiff']} أشهر'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),

            // خيار تحديد تاريخ الاستحقاق يدوياً
            Row(
              children: [
                Checkbox(
                  value: _useCustomDueDate,
                  onChanged: (value) {
                    setState(() {
                      _useCustomDueDate = value ?? false;
                      if (!_useCustomDueDate) {
                        _dueDate = null;
                      }
                    });
                  },
                  activeColor: AppColors.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'تحديد تاريخ الاستحقاق يدوياً',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),

            if (_useCustomDueDate) ...[
              const SizedBox(height: 16),
              InkWell(
                onTap: _selectDueDate,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.secondary),
                    borderRadius: BorderRadius.circular(12),
                    color: AppColors.secondary.withValues(alpha: 0.1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.event, color: AppColors.secondary),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تاريخ الاستحقاق المخصص',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _dueDate != null
                                  ? DateFormat('dd/MM/yyyy', 'en').format(_dueDate!)
                                  : 'اضغط لاختيار التاريخ',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: _dueDate != null
                                    ? AppColors.textPrimary
                                    : AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            AppColors.success.withValues(alpha: 0.1),
            Colors.white,
            AppColors.success.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.success.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.success,
                  AppColors.success.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'ملخص العملية',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'محسوب',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

            // المبلغ الإجمالي
            _buildResultItem(
              'المبلغ الإجمالي',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_totalAmount),
              Icons.attach_money,
              AppColors.success,
              isHighlighted: true,
            ),
            const SizedBox(height: 16),

            // تاريخ الاستحقاق
            _buildResultItem(
              'تاريخ الاستحقاق',
              DateFormat('dd/MM/yyyy', 'en').format(_finalDueDate),
              Icons.event,
              AppColors.info,
            ),
            const SizedBox(height: 16),

            // عدد الأيام
            _buildResultItem(
              'إجمالي الأيام',
              '$_totalDays يوم',
              Icons.today,
              AppColors.warning,
            ),
            const SizedBox(height: 16),

            // المدة بالأشهر
            _buildResultItem(
              'مدة السداد',
              _buildDurationText(),
              Icons.schedule,
              AppColors.secondary,
            ),
          ],
        ),
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color, {
    bool isHighlighted = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isHighlighted
            ? color.withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isHighlighted
              ? color
              : Colors.grey.shade300,
          width: isHighlighted ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isHighlighted
                ? color.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: isHighlighted ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: TextStyle(
                    color: isHighlighted ? color : AppColors.textPrimary,
                    fontSize: isHighlighted ? 20 : 16,
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w600,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
          if (isHighlighted)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'الإجمالي',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOptionalInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ExpansionTile(
        leading: const Icon(Icons.person_add, color: AppColors.primary),
        title: const Text(
          'معلومات إضافية (اختياري)',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildAnimatedTextField(
                  controller: _customerNameController,
                  label: 'اسم العميل',
                  hint: 'أدخل اسم العميل',
                  icon: Icons.person,
                ),
                const SizedBox(height: 16),
                _buildAnimatedTextField(
                  controller: _customerPhoneController,
                  label: 'رقم الهاتف',
                  hint: 'أدخل رقم الهاتف',
                  icon: Icons.phone,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                _buildAnimatedTextField(
                  controller: _productNameController,
                  label: 'اسم المنتج',
                  hint: 'أدخل اسم المنتج',
                  icon: Icons.shopping_bag,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProceedButton() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _totalAmount > 0 ? _proceedToSale : null,
        icon: const Icon(Icons.arrow_forward, size: 24),
        label: const Text(
          'متابعة إلى البيع',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: _totalAmount > 0 ? AppColors.primary : AppColors.textLight,
          foregroundColor: Colors.white,
          elevation: _totalAmount > 0 ? 8 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadowColor: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
    );
  }
}
