# تطبيق مبيعات الآجل - Deferred Sales App

تطبيق احترافي لإدارة مبيعات الآجل مصمم خصيصاً للتجار والشركات التي تبيع بنظام الدفع الآجل.

## المميزات الرئيسية

### 🧮 حاسبة المبيعات الذكية
- حساب المبلغ الإجمالي تلقائياً (عدد الكارتات × السعر)
- اختيار مدة السداد بالأشهر (1-60 شهر)
- عرض تاريخ الاستحقاق وعدد الأيام الإجمالية
- إمكانية إدخال معلومات العميل مسبقاً
- انتقال سلس إلى شاشة إضافة المبيعة مع البيانات المحسوبة

### 📊 لوحة المعلومات
- إحصائيات شاملة للمبيعات
- عرض المبيعات الأخيرة
- تنبيهات للمستحقات القريبة (30 يوم)
- أزرار الإجراءات السريعة

### 📝 إدارة المبيعات
- إضافة مبيعات جديدة بسهولة
- تعديل المبيعات الموجودة
- حذف المبيعات
- تحديد حالة المبيعة (نشطة، مكتملة، متأخرة)

### 🔍 البحث والفلترة
- البحث في المبيعات بالاسم أو المنتج أو رقم الهاتف
- فلترة المبيعات حسب الحالة
- عرض منظم بالتبويبات

### 📱 واجهة مستخدم احترافية
- تصميم عربي أنيق ومتجاوب
- ألوان احترافية ومتناسقة
- رسوم متحركة سلسة
- دعم كامل للغة العربية

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **SQLite**: قاعدة البيانات المحلية
- **Material Design 3**: نظام التصميم
- **Intl**: دعم التواريخ والأرقام العربية

## كيفية الاستخدام

### إضافة مبيعة جديدة
1. اضغط على "حاسبة المبيعات" من الشاشة الرئيسية
2. أدخل عدد الكارتات والسعر
3. اختر مدة السداد
4. أضف معلومات العميل (اختياري)
5. اضغط "متابعة إلى البيع"
6. راجع البيانات واضغط "حفظ"

### متابعة المبيعات
1. من الشاشة الرئيسية، راجع المستحقات القريبة
2. اضغط على أي مبيعة لعرض التفاصيل
3. استخدم الفلاتر لعرض المبيعات حسب الحالة
4. ابحث عن مبيعة معينة باستخدام شريط البحث

## تشغيل التطبيق

```bash
flutter pub get
flutter run
```

**ملاحظة**: هذا التطبيق مصمم خصيصاً لإدارة مبيعات الآجل ولا يتضمن نظام الأقساط كما هو مطلوب.
