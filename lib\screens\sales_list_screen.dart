import 'package:flutter/material.dart';
import '../models/deferred_sale.dart';
import '../services/database_service.dart';
import '../utils/app_colors.dart';
import '../widgets/sale_card.dart';
import 'add_sale_screen.dart';

class SalesListScreen extends StatefulWidget {
  final String? initialFilter;

  const SalesListScreen({super.key, this.initialFilter});

  @override
  State<SalesListScreen> createState() => _SalesListScreenState();
}

class _SalesListScreenState extends State<SalesListScreen>
    with TickerProviderStateMixin {
  final DatabaseService _databaseService = DatabaseService();
  final TextEditingController _searchController = TextEditingController();

  List<DeferredSale> _allSales = [];
  List<DeferredSale> _filteredSales = [];
  String _currentFilter = 'all';
  bool _isLoading = true;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter ?? 'all';
    _tabController = TabController(length: 4, vsync: this);
    _setInitialTab();
    _loadSales();
  }

  void _setInitialTab() {
    switch (_currentFilter) {
      case 'active':
        _tabController.index = 1;
        break;
      case 'overdue':
        _tabController.index = 2;
        break;
      case 'completed':
        _tabController.index = 3;
        break;
      default:
        _tabController.index = 0;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSales() async {
    setState(() => _isLoading = true);

    try {
      final sales = await _databaseService.getAllSales();
      setState(() {
        _allSales = sales;
        _applyFilter();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    }
  }

  void _applyFilter() {
    List<DeferredSale> filtered = _allSales;

    // تطبيق فلتر الحالة
    switch (_currentFilter) {
      case 'active':
        filtered = filtered.where((sale) => sale.currentStatus == 'active').toList();
        break;
      case 'overdue':
        filtered = filtered.where((sale) => sale.currentStatus == 'overdue').toList();
        break;
      case 'completed':
        filtered = filtered.where((sale) => sale.currentStatus == 'completed').toList();
        break;
    }

    // تطبيق البحث
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((sale) {
        return sale.customerName.toLowerCase().contains(searchQuery) ||
            sale.productName.toLowerCase().contains(searchQuery) ||
            sale.customerPhone.contains(searchQuery);
      }).toList();
    }

    setState(() {
      _filteredSales = filtered;
    });
  }

  void _onTabChanged(int index) {
    switch (index) {
      case 0:
        _currentFilter = 'all';
        break;
      case 1:
        _currentFilter = 'active';
        break;
      case 2:
        _currentFilter = 'overdue';
        break;
      case 3:
        _currentFilter = 'completed';
        break;
    }
    _applyFilter();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('جميع المبيعات'),
        bottom: TabBar(
          controller: _tabController,
          onTap: _onTabChanged,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.list, size: 20),
                  const SizedBox(height: 4),
                  Text(
                    'الكل (${_allSales.length})',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.trending_up, size: 20),
                  const SizedBox(height: 4),
                  Text(
                    'نشطة (${_allSales.where((s) => s.currentStatus == 'active').length})',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.warning, size: 20),
                  const SizedBox(height: 4),
                  Text(
                    'متأخرة (${_allSales.where((s) => s.currentStatus == 'overdue').length})',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, size: 20),
                  const SizedBox(height: 4),
                  Text(
                    'مكتملة (${_allSales.where((s) => s.currentStatus == 'completed').length})',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSales,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في المبيعات...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _applyFilter();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.background,
              ),
              onChanged: (_) => _applyFilter(),
            ),
          ),

          // قائمة المبيعات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSales.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                        onRefresh: _loadSales,
                        child: ListView.separated(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredSales.length,
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 12),
                          itemBuilder: (context, index) {
                            return SaleCard(
                              sale: _filteredSales[index],
                              onTap: _loadSales,
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddSaleScreen()),
          );
          if (result == true) {
            _loadSales();
          }
        },
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;

    switch (_currentFilter) {
      case 'active':
        message = 'لا توجد مبيعات نشطة';
        icon = Icons.trending_up;
        break;
      case 'overdue':
        message = 'لا توجد مبيعات متأخرة';
        icon = Icons.warning;
        break;
      case 'completed':
        message = 'لا توجد مبيعات مكتملة';
        icon = Icons.check_circle;
        break;
      default:
        message = _searchController.text.isNotEmpty
            ? 'لا توجد نتائج للبحث'
            : 'لا توجد مبيعات حتى الآن';
        icon = _searchController.text.isNotEmpty ? Icons.search_off : Icons.shopping_cart;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textLight,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          if (_currentFilter == 'all' && _searchController.text.isEmpty) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const AddSaleScreen()),
                );
                if (result == true) {
                  _loadSales();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('إضافة مبيعة جديدة'),
            ),
          ],
        ],
      ),
    );
  }
}
