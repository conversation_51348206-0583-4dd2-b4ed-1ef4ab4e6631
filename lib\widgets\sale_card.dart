import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/deferred_sale.dart';
import '../utils/app_colors.dart';
import '../screens/sale_details_screen.dart';

class SaleCard extends StatelessWidget {
  final DeferredSale sale;
  final VoidCallback? onTap;
  final bool showUrgency;

  const SaleCard({
    super.key,
    required this.sale,
    this.onTap,
    this.showUrgency = false,
  });

  @override
  Widget build(BuildContext context) {
    final currentStatus = sale.currentStatus;
    final statusColor = _getStatusColor(currentStatus);
    final remainingDays = sale.remainingDays;
    final progressPercentage = sale.progressPercentage;

    return Card(
      elevation: 4,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SaleDetailsScreen(sale: sale),
            ),
          );
          onTap?.call();
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: AppColors.cardGradient,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: اسم العميل والحالة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      sale.customerName,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: statusColor, width: 1),
                    ),
                    child: Text(
                      _getStatusText(currentStatus),
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // المنتج
              Text(
                sale.productName,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // المبلغ والمدة
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'المبلغ',
                      NumberFormat.currency(
                        locale: 'ar_SA',
                        symbol: 'ر.س',
                        decimalDigits: 0,
                      ).format(sale.totalAmount),
                      Icons.attach_money,
                      AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'المدة',
                      '${sale.durationMonths} شهر',
                      Icons.schedule,
                      AppColors.info,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // التواريخ
              Row(
                children: [
                  Expanded(
                    child: _buildDateInfo(
                      context,
                      'تاريخ البيع',
                      sale.saleDate,
                      Icons.calendar_today,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDateInfo(
                      context,
                      'تاريخ الاستحقاق',
                      sale.dueDate,
                      Icons.event,
                    ),
                  ),
                ],
              ),

              if (currentStatus == 'active') ...[
                const SizedBox(height: 16),
                
                // شريط التقدم
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'التقدم',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        Text(
                          '${progressPercentage.toStringAsFixed(1)}%',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: progressPercentage / 100,
                      backgroundColor: AppColors.textLight.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _getProgressColor(progressPercentage),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // الأيام المتبقية
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getRemainingDaysColor(remainingDays).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getRemainingDaysColor(remainingDays),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getRemainingDaysIcon(remainingDays),
                        color: _getRemainingDaysColor(remainingDays),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getRemainingDaysText(remainingDays),
                          style: TextStyle(
                            color: _getRemainingDaysColor(remainingDays),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (showUrgency && remainingDays <= 7)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'عاجل',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateInfo(
    BuildContext context,
    String label,
    DateTime date,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 16),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                DateFormat('dd/MM/yyyy', 'en').format(date),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return AppColors.activeStatus;
      case 'completed':
        return AppColors.completedStatus;
      case 'overdue':
        return AppColors.overdueStatus;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'completed':
        return 'مكتملة';
      case 'overdue':
        return 'متأخرة';
      default:
        return 'غير محدد';
    }
  }

  Color _getProgressColor(double percentage) {
    if (percentage < 50) return AppColors.success;
    if (percentage < 80) return AppColors.warning;
    return AppColors.error;
  }

  Color _getRemainingDaysColor(int days) {
    if (days > 30) return AppColors.success;
    if (days > 7) return AppColors.warning;
    return AppColors.error;
  }

  IconData _getRemainingDaysIcon(int days) {
    if (days > 30) return Icons.check_circle;
    if (days > 7) return Icons.warning;
    return Icons.error;
  }

  String _getRemainingDaysText(int days) {
    if (days == 0) return 'مستحقة اليوم';
    if (days == 1) return 'يوم واحد متبقي';
    if (days == 2) return 'يومان متبقيان';
    if (days <= 10) return '$days أيام متبقية';
    return '$days يوم متبقي';
  }
}
