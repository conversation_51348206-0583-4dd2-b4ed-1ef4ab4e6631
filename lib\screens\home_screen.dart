import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/deferred_sale.dart';
import '../services/database_service.dart';
import '../utils/app_colors.dart';
import '../widgets/sale_card.dart';
import '../widgets/statistics_card.dart';
import 'add_sale_screen.dart';
import 'sales_list_screen.dart';
import 'calculator_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DatabaseService _databaseService = DatabaseService();
  Map<String, dynamic> _statistics = {};
  List<DeferredSale> _recentSales = [];
  List<DeferredSale> _upcomingDues = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final statistics = await _databaseService.getStatistics();
      final allSales = await _databaseService.getAllSales();
      
      // أحدث 5 مبيعات
      final recentSales = allSales.take(5).toList();
      
      // المبيعات التي تستحق خلال 30 يوم
      final now = DateTime.now();
      final upcomingDues = allSales.where((sale) {
        final daysRemaining = sale.remainingDays;
        return daysRemaining <= 30 && daysRemaining > 0 && sale.currentStatus == 'active';
      }).take(5).toList();
      
      setState(() {
        _statistics = statistics;
        _recentSales = recentSales;
        _upcomingDues = upcomingDues;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('مبيعات الآجل'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CalculatorScreen(),
              ),
            ).then((_) => _loadData()),
            tooltip: 'حاسبة المبيعات',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الإحصائيات
                    _buildStatisticsSection(),
                    const SizedBox(height: 24),
                    
                    // الإجراءات السريعة
                    _buildQuickActions(),
                    const SizedBox(height: 24),
                    
                    // المبيعات الأخيرة
                    _buildRecentSalesSection(),
                    const SizedBox(height: 24),
                    
                    // المستحقات القريبة
                    _buildUpcomingDuesSection(),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddSaleScreen()),
          );
          if (result == true) {
            _loadData();
          }
        },
        icon: const Icon(Icons.add),
        label: const Text('مبيعة جديدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة عامة',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: StatisticsCard(
                title: 'إجمالي المبيعات',
                value: '${_statistics['totalSales'] ?? 0}',
                icon: Icons.shopping_cart,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatisticsCard(
                title: 'المبلغ الإجمالي',
                value: NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'د.ع',
                  decimalDigits: 0,
                ).format(_statistics['totalAmount'] ?? 0),
                icon: Icons.attach_money,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: StatisticsCard(
                title: 'نشطة',
                value: '${_statistics['activeSales'] ?? 0}',
                icon: Icons.trending_up,
                color: AppColors.activeStatus,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatisticsCard(
                title: 'متأخرة',
                value: '${_statistics['overdueSales'] ?? 0}',
                icon: Icons.warning,
                color: AppColors.overdueStatus,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'حاسبة المبيعات',
                Icons.calculate,
                AppColors.secondary,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CalculatorScreen(),
                  ),
                ).then((_) => _loadData()),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'جميع المبيعات',
                Icons.list,
                AppColors.primary,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SalesListScreen(),
                  ),
                ).then((_) => _loadData()),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'المتأخرة',
                Icons.warning,
                AppColors.overdueStatus,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SalesListScreen(
                      initialFilter: 'overdue',
                    ),
                  ),
                ).then((_) => _loadData()),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'النشطة',
                Icons.trending_up,
                AppColors.activeStatus,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SalesListScreen(
                      initialFilter: 'active',
                    ),
                  ),
                ).then((_) => _loadData()),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32),
          const SizedBox(height: 8),
          Text(title, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildRecentSalesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المبيعات الأخيرة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            TextButton(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SalesListScreen(),
                ),
              ).then((_) => _loadData()),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_recentSales.isEmpty)
          const Center(
            child: Text('لا توجد مبيعات حتى الآن'),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentSales.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return SaleCard(
                sale: _recentSales[index],
                onTap: () => _loadData(),
              );
            },
          ),
      ],
    );
  }

  Widget _buildUpcomingDuesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المستحقات القريبة (30 يوم)',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        if (_upcomingDues.isEmpty)
          const Center(
            child: Text('لا توجد مستحقات قريبة'),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _upcomingDues.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return SaleCard(
                sale: _upcomingDues[index],
                onTap: () => _loadData(),
                showUrgency: true,
              );
            },
          ),
      ],
    );
  }
}
