import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';

class CalculatorResultsScreen extends StatefulWidget {
  final String quantity;
  final String salePrice;
  final String purchasePrice;

  const CalculatorResultsScreen({
    super.key,
    required this.quantity,
    required this.salePrice,
    required this.purchasePrice,
  });

  @override
  State<CalculatorResultsScreen> createState() => _CalculatorResultsScreenState();
}

class _CalculatorResultsScreenState extends State<CalculatorResultsScreen> {
  late double _quantity;
  late double _salePrice;
  late double _purchasePrice;
  late double _totalSaleAmount;
  late double _totalPurchaseAmount;
  late double _totalProfit;
  late double _profitPerCard;
  late double _profitPerCardPerMonth;
  late double _profitPercentage;
  
  // متغيرات إضافية للحسابات
  int _selectedMonths = 12; // افتراضي 12 شهر
  late DateTime _finalDueDate;
  late int _totalDays;
  late List<Map<String, dynamic>> _monthOptions;

  @override
  void initState() {
    super.initState();
    _initializeMonthOptions();
    _calculateResults();
  }

  void _initializeMonthOptions() {
    final now = DateTime.now();

    // أسماء الأشهر الميلادية
    final monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    _monthOptions = [];

    for (int i = 1; i <= 12; i++) {
      int targetMonth = now.month + i;
      int targetYear = now.year;

      // التعامل مع تجاوز السنة
      if (targetMonth > 12) {
        targetYear++;
        targetMonth = targetMonth - 12;
      }

      final targetDate = DateTime(targetYear, targetMonth, now.day);

      // حساب المدة الفعلية من تاريخ البيع إلى التاريخ المستهدف
      final monthsDiff = _calculateMonthsDifference(now, targetDate);

      _monthOptions.add({
        'name': monthNames[targetMonth - 1], // اسم الشهر الفعلي
        'monthsDiff': monthsDiff, // المدة الفعلية
        'date': targetDate,
        'displayText': '${monthNames[targetMonth - 1]} (${monthsDiff == 1 ? "شهر" : "$monthsDiff أشهر"})',
      });
    }
  }

  // دالة لحساب الفرق بالأشهر بين تاريخين
  int _calculateMonthsDifference(DateTime startDate, DateTime endDate) {
    int monthsDiff = (endDate.year - startDate.year) * 12 + (endDate.month - startDate.month);

    // إذا كان اليوم في الشهر المستهدف أقل من يوم البداية، نقص شهر
    if (endDate.day < startDate.day) {
      monthsDiff--;
    }

    return monthsDiff > 0 ? monthsDiff : 1; // على الأقل شهر واحد
  }

  void _calculateResults() {
    _quantity = double.tryParse(widget.quantity.replaceAll(',', '')) ?? 0;
    _salePrice = double.tryParse(widget.salePrice.replaceAll(',', '')) ?? 0;
    _purchasePrice = double.tryParse(widget.purchasePrice.replaceAll(',', '')) ?? 0;

    _totalSaleAmount = _quantity * _salePrice;
    _totalPurchaseAmount = _quantity * _purchasePrice;
    _totalProfit = _totalSaleAmount - _totalPurchaseAmount;
    _profitPerCard = _salePrice - _purchasePrice;

    // حساب الربح الشهري للكارت الواحد
    if (_selectedMonths > 0) {
      _profitPerCardPerMonth = _profitPerCard / _selectedMonths;
    } else {
      _profitPerCardPerMonth = 0;
    }

    // حساب النسبة المئوية للربح
    if (_purchasePrice > 0) {
      _profitPercentage = (_profitPerCard / _purchasePrice) * 100;
    } else {
      _profitPercentage = 0;
    }

    // تحديث التاريخ والأيام
    final now = DateTime.now();
    _finalDueDate = DateTime(now.year, now.month + _selectedMonths, now.day);
    _totalDays = _finalDueDate.difference(now).inDays;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('نتائج الحاسبة'),
        backgroundColor: AppColors.secondary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // قسم اختيار الأشهر
            _buildMonthsSelectionSection(),

            const SizedBox(height: 20),

            // نفس تصميم النتائج من الصفحة الأصلية
            _buildResultsSection(),

            const SizedBox(height: 20),
            
            // زر العودة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(Icons.arrow_back, size: 20),
                label: Text(
                  'العودة للصفحة الرئيسية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // قسم النتائج - نسخة من الصفحة الأصلية
  Widget _buildResultsSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            AppColors.success.withValues(alpha: 0.1),
            Colors.white,
            AppColors.success.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.success.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.success,
                  AppColors.success.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.success.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'ملخص العملية',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'محسوب',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

            // المبلغ الإجمالي للبيع - تصميم مخصص
            _buildCustomTotalSaleCard(),
            const SizedBox(height: 12),

            // المبلغ الإجمالي للشراء
            _buildResultItem(
              'المبلغ الإجمالي للشراء',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_totalPurchaseAmount),
              Icons.shopping_cart,
              AppColors.info,
            ),
            const SizedBox(height: 12),

            // إجمالي الربح
            _buildResultItem(
              'إجمالي الربح',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_totalProfit),
              Icons.trending_up,
              AppColors.success,
            ),
            const SizedBox(height: 12),

            // ربح الكارت الواحد
            _buildResultItem(
              'ربح الكارت الواحد',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_profitPerCard),
              Icons.credit_card,
              AppColors.warning,
            ),
            const SizedBox(height: 12),

            // ربح الكارت الواحد في الشهر
            _buildResultItem(
              'ربح الكارت الواحد في الشهر',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_profitPerCardPerMonth),
              Icons.calendar_month,
              AppColors.secondary,
            ),
            const SizedBox(height: 12),

            // النسبة المئوية للربح
            _buildResultItem(
              'النسبة المئوية للربح',
              '${_profitPercentage.toStringAsFixed(1)}%',
              Icons.percent,
              AppColors.primary,
            ),
            const SizedBox(height: 12),

            // كم تربح جميع الكروت في الشهر
            _buildResultItem(
              'ربح جميع الكروت في الشهر',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'د.ع',
                decimalDigits: 0,
              ).format(_totalProfit / (_selectedMonths > 0 ? _selectedMonths : 1)),
              Icons.account_balance_wallet,
              AppColors.success,
              isHighlighted: true,
            ),
            const SizedBox(height: 16),

            // تاريخ الاستحقاق
            _buildResultItem(
              'تاريخ الاستحقاق',
              DateFormat('dd/MM/yyyy', 'en').format(_finalDueDate),
              Icons.event,
              AppColors.info,
            ),
            const SizedBox(height: 16),

            // عدد الأيام
            _buildResultItem(
              'إجمالي الأيام',
              '$_totalDays يوم',
              Icons.today,
              AppColors.warning,
            ),
            const SizedBox(height: 16),


          ],
        ),
    );
  }

  // بطاقة مخصصة للمبلغ الإجمالي للبيع
  Widget _buildCustomTotalSaleCard() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          // الأيقونة مع تدرج أزرق
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade600,
                  Colors.blue.shade400,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              Icons.attach_money,
              color: Colors.white,
              size: 20,
            ),
          ),

          const SizedBox(width: 20),

          // المحتوى
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المبلغ الإجمالي للبيع',
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.3,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  NumberFormat.currency(
                    locale: 'ar_SA',
                    symbol: 'د.ع',
                    decimalDigits: 0,
                  ).format(_totalSaleAmount),
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),

          // مؤشر الأهمية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade600,
                  Colors.blue.shade400,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'الإجمالي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildResultItem(
    String label,
    String value,
    IconData icon,
    Color color, {
    bool isHighlighted = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isHighlighted
            ? color.withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isHighlighted
              ? color
              : Colors.grey.shade300,
          width: isHighlighted ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isHighlighted
                ? color.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: isHighlighted ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: TextStyle(
                    color: isHighlighted ? color : AppColors.textPrimary,
                    fontSize: isHighlighted ? 20 : 16,
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w600,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
          if (isHighlighted)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'الإجمالي',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // قسم اختيار الأشهر
  Widget _buildMonthsSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calendar_month,
                  color: AppColors.secondary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'اختر مدة السداد بالأشهر:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // قائمة الأشهر الدائرية
          Center(
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              alignment: WrapAlignment.center,
              children: _monthOptions.map((monthData) {
              final isSelected = _selectedMonths == monthData['monthsDiff'];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedMonths = monthData['monthsDiff'];
                    _calculateResults(); // إعادة حساب النتائج
                  });
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? AppColors.secondary : Colors.white,
                    border: Border.all(
                      color: isSelected ? AppColors.secondary : AppColors.textLight,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isSelected
                            ? AppColors.secondary.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.1),
                        blurRadius: isSelected ? 8 : 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        monthData['name'],
                        style: TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${monthData['monthsDiff']}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : AppColors.secondary,
                        ),
                      ),
                      Text(
                        monthData['monthsDiff'] == 1 ? 'شهر' : 'أشهر',
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? Colors.white.withValues(alpha: 0.8) : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
            ),
          ),

          const SizedBox(height: 16),

          // مدة السداد
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.secondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.secondary.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.schedule,
                  color: AppColors.secondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'مدة السداد: ${_buildActualDurationText()}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // معلومات إضافية
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.2)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.info,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'اختر المدة المناسبة لإعادة حساب الأرباح الشهرية',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء نص المدة الفعلية
  String _buildActualDurationText() {
    final now = DateTime.now();
    final actualMonths = _calculateMonthsDifference(now, _finalDueDate);

    if (actualMonths == 1) {
      return 'شهر واحد';
    } else if (actualMonths == 2) {
      return 'شهرين';
    } else if (actualMonths < 12) {
      return '$actualMonths أشهر';
    } else {
      final years = actualMonths ~/ 12;
      final remainingMonths = actualMonths % 12;
      if (remainingMonths == 0) {
        return years == 1 ? 'سنة واحدة' : '$years سنوات';
      } else {
        return '$years سنة و $remainingMonths أشهر';
      }
    }
  }
}