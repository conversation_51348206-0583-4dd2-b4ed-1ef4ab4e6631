import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';

class CalculatorResultsScreen extends StatefulWidget {
  final String quantity;
  final String salePrice;
  final String purchasePrice;

  const CalculatorResultsScreen({
    super.key,
    required this.quantity,
    required this.salePrice,
    required this.purchasePrice,
  });

  @override
  State<CalculatorResultsScreen> createState() => _CalculatorResultsScreenState();
}

class _CalculatorResultsScreenState extends State<CalculatorResultsScreen> {
  late double _quantity;
  late double _salePrice;
  late double _purchasePrice;
  late double _totalSaleAmount;
  late double _totalPurchaseAmount;
  late double _totalProfit;
  late double _profitPerCard;
  late double _profitPercentage;

  @override
  void initState() {
    super.initState();
    _calculateResults();
  }

  void _calculateResults() {
    _quantity = double.tryParse(widget.quantity.replaceAll(',', '')) ?? 0;
    _salePrice = double.tryParse(widget.salePrice.replaceAll(',', '')) ?? 0;
    _purchasePrice = double.tryParse(widget.purchasePrice.replaceAll(',', '')) ?? 0;
    
    _totalSaleAmount = _quantity * _salePrice;
    _totalPurchaseAmount = _quantity * _purchasePrice;
    _totalProfit = _totalSaleAmount - _totalPurchaseAmount;
    _profitPerCard = _salePrice - _purchasePrice;
    
    if (_purchasePrice > 0) {
      _profitPercentage = (_profitPerCard / _purchasePrice) * 100;
    } else {
      _profitPercentage = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('نتائج الحاسبة'),
        backgroundColor: AppColors.secondary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // بطاقة المدخلات
            _buildInputSummaryCard(),
            
            const SizedBox(height: 20),
            
            // بطاقة النتائج الرئيسية
            _buildMainResultsCard(),
            
            const SizedBox(height: 20),
            
            // بطاقة تفاصيل الربح
            _buildProfitDetailsCard(),
            
            const SizedBox(height: 20),
            
            // زر العودة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(Icons.arrow_back, size: 20),
                label: Text(
                  'العودة للصفحة الرئيسية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بطاقة ملخص المدخلات
  Widget _buildInputSummaryCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.input,
                  color: AppColors.info,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'المدخلات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInputRow('عدد الكروت', '${_quantity.toInt()}', 'كارت'),
          const SizedBox(height: 8),
          _buildInputRow('سعر البيع', NumberFormat('#,##0', 'en').format(_salePrice), 'د.ع'),
          const SizedBox(height: 8),
          _buildInputRow('سعر الشراء', NumberFormat('#,##0', 'en').format(_purchasePrice), 'د.ع'),
        ],
      ),
    );
  }

  Widget _buildInputRow(String label, String value, String unit) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          '$value $unit',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  // بطاقة النتائج الرئيسية
  Widget _buildMainResultsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade600,
            Colors.blue.shade400,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.attach_money,
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            'المبلغ الإجمالي للبيع',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            NumberFormat.currency(
              locale: 'ar_SA',
              symbol: 'د.ع',
              decimalDigits: 0,
            ).format(_totalSaleAmount),
            style: TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // بطاقة تفاصيل الربح
  Widget _buildProfitDetailsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.trending_up,
                  color: AppColors.success,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'تفاصيل الربح',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildProfitRow(
            'المبلغ الإجمالي للشراء',
            NumberFormat.currency(
              locale: 'ar_SA',
              symbol: 'د.ع',
              decimalDigits: 0,
            ).format(_totalPurchaseAmount),
            AppColors.warning,
          ),
          const SizedBox(height: 12),
          _buildProfitRow(
            'إجمالي الربح',
            NumberFormat.currency(
              locale: 'ar_SA',
              symbol: 'د.ع',
              decimalDigits: 0,
            ).format(_totalProfit),
            AppColors.success,
            isHighlighted: true,
          ),
          const SizedBox(height: 12),
          _buildProfitRow(
            'ربح الكارت الواحد',
            NumberFormat.currency(
              locale: 'ar_SA',
              symbol: 'د.ع',
              decimalDigits: 0,
            ).format(_profitPerCard),
            AppColors.secondary,
          ),
          const SizedBox(height: 12),
          _buildProfitRow(
            'النسبة المئوية للربح',
            '${_profitPercentage.toStringAsFixed(1)}%',
            AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildProfitRow(String label, String value, Color color, {bool isHighlighted = false}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isHighlighted ? color.withValues(alpha: 0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isHighlighted ? Border.all(color: color.withValues(alpha: 0.3)) : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isHighlighted ? color : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
