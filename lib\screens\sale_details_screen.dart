import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/deferred_sale.dart';
import '../services/database_service.dart';
import '../utils/app_colors.dart';
import 'add_sale_screen.dart';

class SaleDetailsScreen extends StatefulWidget {
  final DeferredSale sale;

  const SaleDetailsScreen({super.key, required this.sale});

  @override
  State<SaleDetailsScreen> createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  late DeferredSale _sale;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _sale = widget.sale;
  }

  Future<void> _markAsCompleted() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإكمال'),
        content: const Text('هل أنت متأكد من أن هذه المبيعة قد تم سدادها بالكامل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      final updatedSale = _sale.copyWith(status: 'completed');
      await _databaseService.updateSale(updatedSale);
      
      setState(() {
        _sale = updatedSale;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث حالة المبيعة إلى مكتملة'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الحالة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _deleteSale() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه المبيعة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      await _databaseService.deleteSale(_sale.id!);
      
      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المبيعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المبيعة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentStatus = _sale.currentStatus;
    final statusColor = _getStatusColor(currentStatus);
    final remainingDays = _sale.remainingDays;
    final progressPercentage = _sale.progressPercentage;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('تفاصيل المبيعة'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'edit':
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddSaleScreen(saleToEdit: _sale),
                    ),
                  );
                  if (result == true && mounted) {
                    Navigator.pop(context, true);
                  }
                  break;
                case 'complete':
                  _markAsCompleted();
                  break;
                case 'delete':
                  _deleteSale();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: AppColors.primary),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              if (currentStatus == 'active')
                const PopupMenuItem(
                  value: 'complete',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AppColors.success),
                      SizedBox(width: 8),
                      Text('تم السداد'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('حذف'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // حالة المبيعة
                  _buildStatusCard(currentStatus, statusColor),
                  const SizedBox(height: 24),

                  // معلومات العميل
                  _buildSectionCard(
                    'معلومات العميل',
                    Icons.person,
                    [
                      _buildInfoRow('الاسم', _sale.customerName, Icons.person),
                      _buildInfoRow('الهاتف', _sale.customerPhone, Icons.phone),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // معلومات المنتج
                  _buildSectionCard(
                    'معلومات المنتج',
                    Icons.shopping_bag,
                    [
                      _buildInfoRow('المنتج', _sale.productName, Icons.shopping_bag),
                      _buildInfoRow(
                        'المبلغ الإجمالي',
                        NumberFormat.currency(
                          locale: 'ar_SA',
                          symbol: 'ر.س',
                          decimalDigits: 0,
                        ).format(_sale.totalAmount),
                        Icons.attach_money,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // معلومات التوقيت
                  _buildSectionCard(
                    'معلومات التوقيت',
                    Icons.schedule,
                    [
                      _buildInfoRow(
                        'تاريخ البيع',
                        DateFormat('dd/MM/yyyy', 'ar').format(_sale.saleDate),
                        Icons.calendar_today,
                      ),
                      _buildInfoRow(
                        'تاريخ الاستحقاق',
                        DateFormat('dd/MM/yyyy', 'ar').format(_sale.dueDate),
                        Icons.event,
                      ),
                      _buildInfoRow(
                        'مدة السداد',
                        '${_sale.durationMonths} شهر',
                        Icons.schedule,
                      ),
                    ],
                  ),

                  if (currentStatus == 'active') ...[
                    const SizedBox(height: 16),
                    _buildProgressCard(progressPercentage, remainingDays),
                  ],

                  if (_sale.notes != null && _sale.notes!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildNotesCard(),
                  ],

                  const SizedBox(height: 32),

                  // أزرار الإجراءات
                  if (currentStatus == 'active')
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _markAsCompleted,
                        icon: const Icon(Icons.check_circle),
                        label: const Text('تم السداد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.success,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusCard(String status, Color statusColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor, width: 2),
      ),
      child: Column(
        children: [
          Icon(
            _getStatusIcon(status),
            color: statusColor,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            _getStatusText(status),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getStatusDescription(status),
            style: TextStyle(
              fontSize: 14,
              color: statusColor.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: AppColors.textSecondary, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(double progressPercentage, int remainingDays) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.timeline, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'تقدم السداد',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // شريط التقدم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('التقدم'),
                Text(
                  '${progressPercentage.toStringAsFixed(1)}%',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progressPercentage / 100,
              backgroundColor: AppColors.textLight.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(progressPercentage),
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 20),

            // الأيام المتبقية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getRemainingDaysColor(remainingDays).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getRemainingDaysColor(remainingDays),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getRemainingDaysIcon(remainingDays),
                    color: _getRemainingDaysColor(remainingDays),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _getRemainingDaysText(remainingDays),
                      style: TextStyle(
                        color: _getRemainingDaysColor(remainingDays),
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.note, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'ملاحظات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _sale.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return AppColors.activeStatus;
      case 'completed':
        return AppColors.completedStatus;
      case 'overdue':
        return AppColors.overdueStatus;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'active':
        return Icons.trending_up;
      case 'completed':
        return Icons.check_circle;
      case 'overdue':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'completed':
        return 'مكتملة';
      case 'overdue':
        return 'متأخرة';
      default:
        return 'غير محدد';
    }
  }

  String _getStatusDescription(String status) {
    switch (status) {
      case 'active':
        return 'المبيعة نشطة ولم تستحق بعد';
      case 'completed':
        return 'تم سداد المبيعة بالكامل';
      case 'overdue':
        return 'المبيعة متأخرة عن موعد الاستحقاق';
      default:
        return '';
    }
  }

  Color _getProgressColor(double percentage) {
    if (percentage < 50) return AppColors.success;
    if (percentage < 80) return AppColors.warning;
    return AppColors.error;
  }

  Color _getRemainingDaysColor(int days) {
    if (days > 30) return AppColors.success;
    if (days > 7) return AppColors.warning;
    return AppColors.error;
  }

  IconData _getRemainingDaysIcon(int days) {
    if (days > 30) return Icons.check_circle;
    if (days > 7) return Icons.warning;
    return Icons.error;
  }

  String _getRemainingDaysText(int days) {
    if (days == 0) return 'مستحقة اليوم';
    if (days == 1) return 'يوم واحد متبقي';
    if (days == 2) return 'يومان متبقيان';
    if (days <= 10) return '$days أيام متبقية';
    return '$days يوم متبقي';
  }
}
