import 'package:flutter/material.dart';

void main() {
  runApp(const DeferredSalesApp());
}

class DeferredSalesApp extends StatelessWidget {
  const DeferredSalesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Deferred Sales App',
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('مبيعات الآجل'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'مرحباً بك في تطبيق مبيعات الآجل',
            style: TextStyle(fontSize: 24),
            textDirection: TextDirection.rtl,
          ),
        ),
      ),
    );
  }
}

class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'خطأ في التطبيق',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('خطأ في التطبيق'),
        ),
        body: const Center(
          child: Text(
            'حدث خطأ في تشغيل التطبيق',
            style: TextStyle(fontSize: 18),
          ),
        ),
      ),
    );
  }
}


