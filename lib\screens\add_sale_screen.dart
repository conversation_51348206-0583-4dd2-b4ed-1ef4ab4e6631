import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/deferred_sale.dart';
import '../services/database_service.dart';
import '../utils/app_colors.dart';

class AddSaleScreen extends StatefulWidget {
  final DeferredSale? saleToEdit;

  const AddSaleScreen({super.key, this.saleToEdit});

  @override
  State<AddSaleScreen> createState() => _AddSaleScreenState();
}

class _AddSaleScreenState extends State<AddSaleScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseService _databaseService = DatabaseService();

  // Controllers
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _productNameController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _saleDate = DateTime.now();
  int _durationMonths = 1;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.saleToEdit != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final sale = widget.saleToEdit!;
    _customerNameController.text = sale.customerName;
    _customerPhoneController.text = sale.customerPhone;
    _productNameController.text = sale.productName;
    _totalAmountController.text = sale.totalAmount.toString();
    _notesController.text = sale.notes ?? '';
    _saleDate = sale.saleDate;
    _durationMonths = sale.durationMonths;
  }

  @override
  void dispose() {
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _productNameController.dispose();
    _totalAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  DateTime get _dueDate {
    return DateTime(
      _saleDate.year,
      _saleDate.month + _durationMonths,
      _saleDate.day,
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _saleDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null && picked != _saleDate) {
      setState(() {
        _saleDate = picked;
      });
    }
  }

  Future<void> _saveSale() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final sale = DeferredSale(
        id: widget.saleToEdit?.id,
        customerName: _customerNameController.text.trim(),
        customerPhone: _customerPhoneController.text.trim(),
        productName: _productNameController.text.trim(),
        totalAmount: double.parse(_totalAmountController.text),
        saleDate: _saleDate,
        dueDate: _dueDate,
        durationMonths: _durationMonths,
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
      );

      if (widget.saleToEdit != null) {
        await _databaseService.updateSale(sale);
      } else {
        await _databaseService.insertSale(sale);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.saleToEdit != null 
                  ? 'تم تحديث المبيعة بنجاح' 
                  : 'تم إضافة المبيعة بنجاح',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المبيعة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          widget.saleToEdit != null ? 'تعديل المبيعة' : 'مبيعة جديدة',
        ),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveSale,
              child: const Text(
                'حفظ',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات العميل
              _buildSectionTitle('معلومات العميل'),
              const SizedBox(height: 16),
              _buildCustomerNameField(),
              const SizedBox(height: 16),
              _buildCustomerPhoneField(),
              const SizedBox(height: 24),

              // معلومات المنتج
              _buildSectionTitle('معلومات المنتج'),
              const SizedBox(height: 16),
              _buildProductNameField(),
              const SizedBox(height: 16),
              _buildTotalAmountField(),
              const SizedBox(height: 24),

              // معلومات البيع
              _buildSectionTitle('معلومات البيع'),
              const SizedBox(height: 16),
              _buildSaleDateField(),
              const SizedBox(height: 16),
              _buildDurationField(),
              const SizedBox(height: 16),
              _buildDueDateInfo(),
              const SizedBox(height: 24),

              // ملاحظات
              _buildSectionTitle('ملاحظات (اختياري)'),
              const SizedBox(height: 16),
              _buildNotesField(),
              const SizedBox(height: 32),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveSale,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          widget.saleToEdit != null ? 'تحديث المبيعة' : 'إضافة المبيعة',
                          style: const TextStyle(fontSize: 16),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildCustomerNameField() {
    return TextFormField(
      controller: _customerNameController,
      decoration: const InputDecoration(
        labelText: 'اسم العميل',
        hintText: 'أدخل اسم العميل',
        prefixIcon: Icon(Icons.person),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم العميل';
        }
        return null;
      },
    );
  }

  Widget _buildCustomerPhoneField() {
    return TextFormField(
      controller: _customerPhoneController,
      decoration: const InputDecoration(
        labelText: 'رقم الهاتف',
        hintText: 'أدخل رقم هاتف العميل',
        prefixIcon: Icon(Icons.phone),
      ),
      keyboardType: TextInputType.phone,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال رقم الهاتف';
        }
        return null;
      },
    );
  }

  Widget _buildProductNameField() {
    return TextFormField(
      controller: _productNameController,
      decoration: const InputDecoration(
        labelText: 'اسم المنتج',
        hintText: 'أدخل اسم المنتج أو الخدمة',
        prefixIcon: Icon(Icons.shopping_bag),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم المنتج';
        }
        return null;
      },
    );
  }

  Widget _buildTotalAmountField() {
    return TextFormField(
      controller: _totalAmountController,
      decoration: const InputDecoration(
        labelText: 'المبلغ الإجمالي',
        hintText: 'أدخل المبلغ بالريال السعودي',
        prefixIcon: Icon(Icons.attach_money),
        suffixText: 'ر.س',
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال المبلغ';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'يرجى إدخال مبلغ صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildSaleDateField() {
    return InkWell(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.textLight),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: AppColors.textSecondary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تاريخ البيع',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('dd/MM/yyyy', 'ar').format(_saleDate),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.textLight),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.schedule, color: AppColors.textSecondary),
              const SizedBox(width: 12),
              const Text(
                'مدة السداد',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _durationMonths.toDouble(),
                  min: 1,
                  max: 60,
                  divisions: 59,
                  activeColor: AppColors.primary,
                  onChanged: (value) {
                    setState(() {
                      _durationMonths = value.round();
                    });
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '$_durationMonths شهر',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.info.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.info.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: AppColors.info),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تاريخ الاستحقاق',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat('dd/MM/yyyy', 'ar').format(_dueDate),
                  style: TextStyle(
                    color: AppColors.info,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'ملاحظات',
        hintText: 'أضف أي ملاحظات إضافية...',
        prefixIcon: Icon(Icons.note),
        alignLabelWithHint: true,
      ),
      maxLines: 3,
      textInputAction: TextInputAction.done,
    );
  }
}
